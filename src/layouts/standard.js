// Next, React, Tw
import { useRef, useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/router';
import Image from 'next/image';
import Link from 'next/link';

// Mui
import { useMediaQuery, SwipeableDrawer } from '@mui/material';
import { Menu } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';

// Components
import Header from './Header';
import LoadingScreen from '../components/Shared/loading-screen';
import NpsPopup from '../components/Shared/NpsPopup';

// Others
import AuthGuard from '../utils/auth/AuthGuard';
import { toUpperCaseFirstLetter } from '../utils/shared';
import { fetchAllStaffs } from '../utils/store/aumReducer';
import { useAuthContext } from '../utils/auth/useAuthContext';
import { setPage } from '../utils/store/simiReducer';

// ----------------------------------------------------------------------

StandardLayout.propTypes = {
  children: PropTypes.node,
  tabs: PropTypes.node,
};

// ----------------------------------------------------------------------

export default function StandardLayout({ tabs, children }) {
  // Standard
  const { isLoading } = useSelector((state) => state?.loading);
  const { breadCrumbsList } = useSelector((state) => state?.simi);
  const isSmallScreen = useMediaQuery('(max-width: 768px)');
  const { asPath } = useRouter();
  const dispatch = useDispatch();
  const { isAuthenticated } = useAuthContext();
  const pageContainerRef = useRef();
  const hiddenDivRef = useRef();

  const [drawerOpen, setDrawerOpen] = useState(false);

  // Others

  const getBreadCrumb = () => {
    const routes = asPath
      ?.split('?')[0]
      ?.split('/')
      ?.filter((o) => o !== '');

    let tabObjects = [];

    for (let i = 0; i < routes.length; i += 1) {
      tabObjects.push({
        label: toUpperCaseFirstLetter(routes[i]?.replaceAll('-', ' ')),
        linkTo: `/${routes.slice(0, i + 1).join('/')}`,
      });
    }

    const replaceCertainWord = (word) => {
      if (word === 'Hsba') return 'HSBA';
      if (word === 'Loa') return 'LOA';
      if (word === 'Wse') return 'WSE';
      if (word?.includes('Cx')) return word?.replaceAll('Cx', 'CX');
      if (word === 'Ptw') return 'PTW';
      if (word?.includes('Cdn')) return word?.replaceAll('Cdn', 'CDN');
      if (word?.includes('Btu')) return word?.replaceAll('Btu', 'BTU');
      if (word === 'Capry') return 'CAPRY';
      if (word?.includes('Solid')) return word?.replaceAll('Solid', 'SOLID');
      if (word?.includes('Facility')) return word?.replaceAll('Facility', 'Facility');
      if (word?.includes('Fa')) return word?.replaceAll('Fa', 'FA');
      if (word?.includes('Pmcare')) return word?.replaceAll('Pmcare', 'PMCARE');
      if (word?.includes('Itaf')) return word?.replaceAll('Itaf', 'ITAF');
      if (word?.includes('Gaisha')) return word?.replaceAll('Gaisha', 'GAISHA');
      if (word?.includes('Sdp')) return word?.replaceAll('Sdp', 'SDP');
      if (word?.includes('Rfs')) return word?.replaceAll('Rfs', 'RFS');
      if (word?.includes('Oasys')) return word?.replaceAll('Oasys', 'OASYS');
      if (word?.includes('Noc')) return word?.replaceAll('Noc', 'NOC');
      if (word?.includes('Ocm')) return word?.replaceAll('Ocm', 'OCM');
      if (/^[0-9a-fA-F]{24}$/.test(word)) return 'Details';
      return word;
    };

    tabObjects = tabObjects?.map((o) => ({ label: replaceCertainWord(o.label), linkTo: o.linkTo }));

    return (
      <div className="flex items-center gap-2 text-xs font-bold">
        {tabObjects.map((o, index) => (
          <div key={index} className="flex gap-2">
            <Link href={o.linkTo}>
              <p className="whitespace-nowrap hover:cursor-pointer hover:underline">{o?.label}</p>
            </Link>

            {index !== tabObjects.length - 1 && <p>/</p>}
          </div>
        ))}
      </div>
    );
  };

  const getCustomBreadCrumb = () => (
    <div className="flex items-center gap-2 text-xs font-bold">
      {breadCrumbsList.map((o, i) => (
        <div key={i} className="flex gap-2">
          <Link href={o.linkTo || o.path || '#'}>
            <p className="whitespace-nowrap hover:cursor-pointer hover:underline">{o?.label}</p>
          </Link>
          {i !== breadCrumbsList?.length - 1 && <p>/</p>}
        </div>
      ))}
    </div>
  );

  const currentModule = asPath?.split('?')[0]?.split('/')[1];

  useEffect(() => {
    setDrawerOpen(false);
    dispatch(setPage(0));
  }, [asPath]);

  useEffect(() => {
    if (!isAuthenticated) return;
    dispatch(fetchAllStaffs());
  }, [isAuthenticated]);

  return (
    <AuthGuard>
      <div className="flex h-full w-full flex-col">
        <div className="w-full">
          <Header hideHeaderAndSideMenu={() => pageContainerRef.current.requestFullscreen()} />
          <div className="flex w-full items-center justify-start gap-4 bg-white pr-8 dark:bg-gray-600 dark:text-white">
            {!['dashboard', 'digital-card']?.includes(currentModule) && (
              <>
                <div className="relative flex h-[40px] w-[40px] items-center justify-center">
                  {!isSmallScreen && (
                    <Image
                      src={`/assets/moduleLogo/${currentModule}.webp`}
                      alt="Module Logo"
                      width={1004}
                      height={1004}
                      className="h-[40px] w-[40px]"
                      loading="eager"
                      priority
                    />
                  )}
                  {isSmallScreen && (
                    <button type="button" onClick={() => setDrawerOpen(true)}>
                      <Menu />
                    </button>
                  )}
                </div>
                {!isSmallScreen && breadCrumbsList?.length === 0 && getBreadCrumb()}
                {!isSmallScreen && breadCrumbsList?.length > 0 && getCustomBreadCrumb()}
              </>
            )}
          </div>
        </div>
        <div className="relative w-full flex-grow ">
          <div
            ref={pageContainerRef}
            id="scroll-container"
            className="absolute left-0 top-0 h-full w-full overflow-y-auto bg-[#f5f5f5] scrollbar scrollbar-none dark:bg-gray-800 md:left-[40px] md:w-[calc(100%_-_40px)]"
          >
            <div ref={hiddenDivRef} />
            {children}
          </div>
          {!isSmallScreen && (
            <div className="absolute left-0 top-0 flex h-full w-[40px] flex-col items-center bg-white py-4 dark:bg-gray-600">
              {tabs}
            </div>
          )}
          <div className="absolute bottom-0 left-1/2 mb-8 flex w-full -translate-x-1/2 transform justify-center ">
            <NpsPopup />
          </div>
          <SwipeableDrawer
            open={drawerOpen}
            onOpen={() => setDrawerOpen(true)}
            onClose={() => setDrawerOpen(false)}
          >
            <div className="max-w-[600px] overflow-x-clip px-2 py-8">{tabs}</div>
          </SwipeableDrawer>
        </div>
      </div>
      {isLoading && <LoadingScreen />}
    </AuthGuard>
  );
}

// Next, React, Tailwind
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSelector, useDispatch } from 'react-redux';

// Packages
import PropTypes from 'prop-types';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';
import UnauthorizedPage from '../unauthorized';
import ErrorBoundary from '../../components/fa/ErrorBoundary';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { setBreadCrumbsList } from '../../utils/store/simiReducer';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  // Standard and Vars
  const { asPath, query } = useRouter();
  const { projectId } = query;
  const { userModuleRole, isAdmin } = useModuleRoleContext();
  const { user, isAuthenticated } = useAuthContext();
  const dispatch = useDispatch();

  const tabList = [
    {
      label: 'Dashboard',
      redirectTo: '/project-tracker',
      icon: 'Dashboard',
      authorized: ['superadmin', 'admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Projects',
      redirectTo: '/project-tracker#projects',
      icon: 'Assignment',
      authorized: ['superadmin', 'admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Analytics',
      redirectTo: '/project-tracker/analytics',
      icon: 'BarChart',
      authorized: ['superadmin', 'admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Reports',
      redirectTo: '/project-tracker/reports',
      icon: 'Assessment',
      authorized: ['superadmin', 'admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Access Management',
      redirectTo: '/project-tracker/access-management',
      icon: 'AdminPanelSettings',
      authorized: ['superadmin', 'admin']?.includes(userModuleRole),
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  let authorized = tabList?.find((o) =>
    asPath?.split('?')?.[0]?.includes(o?.redirectTo?.split('#')?.[0])
  )?.authorized;

  // Special handling for project detail pages
  const projectPattern = /\/project-tracker\/project\/[a-zA-Z0-9]+/;
  if (projectPattern.test(asPath)) {
    // For project detail pages, check if user has access to projects in general
    authorized = ['superadmin', 'admin', 'user']?.includes(userModuleRole);
  }

  // If user is not authenticated, they shouldn't have access
  if (!isAuthenticated) {
    authorized = false;
  }

  useEffect(() => {
    // Set default breadcrumbs for the module
    if (asPath === '/project-tracker') {
      dispatch(setBreadCrumbsList([
        { label: 'Home', linkTo: '/' },
        { label: 'Project Tracker', linkTo: '/project-tracker' }
      ]));
    }
  }, [dispatch, asPath]);

  return (
    <StandardLayout 
      tabs={tabs} 
      moduleColor="#3B82F6" // Blue color theme for Project Tracker
      moduleName="Project Tracker"
      moduleIcon="Assignment"
    >
      <ErrorBoundary>
        {authorized ? children : <UnauthorizedPage />}
      </ErrorBoundary>
    </StandardLayout>
  );
}
// Next, React, Tw
import { useRouter } from 'next/router';
import { useSelector, useDispatch } from 'react-redux';
import { createContext, useEffect, useReducer, useContext, useMemo } from 'react';

// Packages
import PropTypes from 'prop-types';

// Others
import { useAuthContext } from './useAuthContext';
import { fetchUserModules, fetchUserSubModules } from '../store/aumReducer';
import { getModuleFromPath } from '../shared';

const initialState = {
  userModuleRole: null,
  isAdmin: null,
};

const reducer = (state, action) => {
  if (action.type === 'SWITCH') {
    return {
      userModuleRole: action.payload.userModuleRole,
      isAdmin: action.payload.isAdmin,
    };
  }
  return state;
};

// ----------------------------------------------------------------------

export const ModuleRoleContext = createContext(null);

// ----------------------------------------------------------------------

const ModuleRoleProvider = ({ children }) => {
  // Standard and Vars
  const { asPath } = useRouter();
  const { user } = useAuthContext();
  const dispatch2 = useDispatch();

  const { userModules } = useSelector((state) => state?.aum);
  const [state, dispatch] = useReducer(reducer, initialState);
  const module = getModuleFromPath(asPath);

  // Others

  const getUserModule = async () => {
    const temp = userModules?.find((o) => o?.module === module);
    dispatch({
      type: 'SWITCH',
      payload: {
        userModuleRole: temp?.role,
        isAdmin: ['superadmin', 'admin']?.includes(temp?.role),
      },
    });
  };

  useEffect(() => {
    if (!user?.staff_id || !module) return;
    if (userModules?.length === 0) {
      dispatch2(fetchUserModules(user?.staff_id));
      return;
    }
    dispatch2(fetchUserSubModules({ module, staffId: user?.staff_id }));

    getUserModule();
  }, [userModules?.length, module, user?.staff_id]);

  const memoizedValue = useMemo(
    () => ({
      userModuleRole: state.userModuleRole,
      isAdmin: state.isAdmin,
    }),
    [state.userModuleRole, state.isAdmin]
  );

  return <ModuleRoleContext.Provider value={memoizedValue}>{children}</ModuleRoleContext.Provider>;
};

ModuleRoleProvider.propTypes = {
  children: PropTypes.node,
};

export default ModuleRoleProvider;

export const useModuleRoleContext = () => {
  const context = useContext(ModuleRoleContext);

  if (!context) throw new Error('useAuthContext context must be use inside AuthProvider');

  return context;
};

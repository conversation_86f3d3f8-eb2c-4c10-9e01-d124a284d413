// Next, React, Tailwind
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Material UI
import {
  Tabs,
  Tab,
  Box,
  Typography,
  Breadcrumbs,
  Link as MuiLink
} from '@mui/material';
import { Home, Dashboard, Assignment } from '@mui/icons-material';

// Components
import Layout from '../../layouts/module/project-tracker';
import DashboardStats from '../../components/project-tracker/DashboardStats';
import ProjectList from '../../components/project-tracker/ProjectList';

// Others
import { fetchDashboardStats } from '../../utils/store/projectTrackerReducer';
import { setBreadCrumbsList } from '../../utils/store/simiReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`project-tracker-tabpanel-${index}`}
      aria-labelledby={`project-tracker-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box className="pt-6">
          {children}
        </Box>
      )}
    </div>
  );
}

export default function Page() {
  const dispatch = useDispatch();
  const [currentTab, setCurrentTab] = useState(0);

  useEffect(() => {
    // Set breadcrumbs
    dispatch(setBreadCrumbsList([
      { label: 'Home', linkTo: '/', icon: <Home fontSize="small" /> },
      { label: 'Project Tracker', linkTo: '/project-tracker', icon: <Assignment fontSize="small" /> }
    ]));

    // Fetch initial data
    dispatch(fetchDashboardStats());
  }, [dispatch]);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const tabs = [
    {
      label: 'Dashboard',
      icon: <Dashboard className="w-5 h-5" />,
      component: <DashboardStats />
    },
    {
      label: 'Projects',
      icon: <Assignment className="w-5 h-5" />,
      component: <ProjectList />
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="h3" className="font-bold text-gray-900 dark:text-white mb-2">
                Project Tracker
              </Typography>
              <Typography variant="body1" className="text-gray-600 dark:text-gray-400">
                Manage and monitor your projects with comprehensive tracking and analytics
              </Typography>
            </div>
          </div>

          {/* Breadcrumbs */}
          <div className="mt-4">
            <Breadcrumbs 
              aria-label="breadcrumb"
              className="text-gray-600 dark:text-gray-400"
            >
              <MuiLink
                color="inherit"
                href="/"
                className="flex items-center hover:text-blue-600"
                underline="hover"
              >
                <Home className="mr-1" fontSize="small" />
                Home
              </MuiLink>
              <Typography color="text.primary" className="flex items-center">
                <Assignment className="mr-1" fontSize="small" />
                Project Tracker
              </Typography>
            </Breadcrumbs>
          </div>
        </div>

        {/* Tabs Navigation */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              aria-label="project tracker tabs"
              className="px-6"
            >
              {tabs.map((tab, index) => (
                <Tab
                  key={index}
                  label={
                    <div className="flex items-center space-x-2">
                      {tab.icon}
                      <span>{tab.label}</span>
                    </div>
                  }
                  id={`project-tracker-tab-${index}`}
                  aria-controls={`project-tracker-tabpanel-${index}`}
                  className="normal-case"
                />
              ))}
            </Tabs>
          </Box>

          {/* Tab Panels */}
          {tabs.map((tab, index) => (
            <TabPanel key={index} value={currentTab} index={index}>
              <div className="px-6 pb-6">
                {tab.component}
              </div>
            </TabPanel>
          ))}
        </div>
      </div>
    </div>
  );
}
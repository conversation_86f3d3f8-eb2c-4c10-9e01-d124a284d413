// Next, React, Tailwind
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';

// Material UI
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  Box,
  Avatar,
  AvatarGroup,
  LinearProgress,
  Chip,
  Breadcrumbs,
  Link as MuiLink,
  IconButton,
  Menu,
  MenuItem,
  Divider
} from '@mui/material';
import {
  Home,
  Assignment,
  Edit,
  MoreVert,
  Timeline,
  People,
  Warning,
  TrendingUp,
  CheckCircle,
  Schedule,
  ChatBubbleOutline
} from '@mui/icons-material';

// Components
import Layout from '../../../layouts/module/project-tracker';
import ProjectTimeline from '../../../components/project-tracker/ProjectTimeline';
import MilestoneManager from '../../../components/project-tracker/MilestoneManager';
import RiskManager from '../../../components/project-tracker/RiskManager';
import TeamManager from '../../../components/project-tracker/TeamManager';
import ActivityFeed from '../../../components/project-tracker/ActivityFeed';
import { EnhancedButton } from '../../../components/fa/ui/EnhancedButton';

// Others
import { 
  fetchProjectDetails, 
  clearCurrentProject,
  updateProject 
} from '../../../utils/store/projectTrackerReducer';
import { setBreadCrumbsList } from '../../../utils/store/simiReducer';
import { useProjectTrackerContext } from '../../../utils/project-tracker';
import { formatDistanceToNow } from 'date-fns';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`project-detail-tabpanel-${index}`}
      aria-labelledby={`project-detail-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box className="pt-6">
          {children}
        </Box>
      )}
    </div>
  );
}

export default function Page() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { projectId } = router.query;
  
  const { 
    currentProject, 
    activities, 
    milestones, 
    risks, 
    team,
    loading: { projectDetails: isLoading } 
  } = useSelector((state) => state.projectTracker);

  const { 
    getHealthColor, 
    getStatusColor, 
    getPriorityColor, 
    calculateProjectHealth 
  } = useProjectTrackerContext();

  const [currentTab, setCurrentTab] = useState(0);
  const [menuAnchor, setMenuAnchor] = useState(null);

  useEffect(() => {
    if (projectId) {
      dispatch(fetchProjectDetails(projectId));
    }
    
    return () => {
      dispatch(clearCurrentProject());
    };
  }, [dispatch, projectId]);

  useEffect(() => {
    if (currentProject?.name && projectId) {
      dispatch(setBreadCrumbsList([
        { label: 'Home', linkTo: '/', icon: <Home fontSize="small" /> },
        { label: 'Project Tracker', linkTo: '/project-tracker', icon: <Assignment fontSize="small" /> },
        { label: currentProject.name, linkTo: `/project-tracker/project/${projectId}` }
      ]));
    }
  }, [dispatch, currentProject, projectId]);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleMenuOpen = (event) => {
    setMenuAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
  };

  const handleUpdateProgress = useCallback(async (newProgress) => {
    try {
      await dispatch(updateProject({
        projectId: currentProject.id,
        updates: { progress_percentage: newProgress }
      })).unwrap();
    } catch (error) {
      console.error('Failed to update progress:', error);
    }
  }, [dispatch, currentProject.id]);

  if (isLoading || !currentProject.id) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="h-40 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-40 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-40 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const health = calculateProjectHealth(currentProject);
  const healthColor = getHealthColor(health);
  const statusColor = getStatusColor(currentProject.status);
  const priorityColor = getPriorityColor(currentProject.priority);

  const tabs = [
    {
      label: 'Overview',
      icon: <Timeline className="w-5 h-5" />,
      component: <ActivityFeed activities={activities} project={currentProject} />
    },
    {
      label: 'Timeline',
      icon: <Schedule className="w-5 h-5" />,
      component: <ProjectTimeline project={currentProject} milestones={milestones} />
    },
    {
      label: 'Milestones',
      icon: <CheckCircle className="w-5 h-5" />,
      component: <MilestoneManager projectId={currentProject.id} milestones={milestones} />
    },
    {
      label: 'Risks',
      icon: <Warning className="w-5 h-5" />,
      component: <RiskManager projectId={currentProject.id} risks={risks} />
    },
    {
      label: 'Team',
      icon: <People className="w-5 h-5" />,
      component: <TeamManager projectId={currentProject.id} team={team} />
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-6">
        {/* Breadcrumbs */}
        <div className="mb-6">
          <Breadcrumbs 
            aria-label="breadcrumb"
            className="text-gray-600 dark:text-gray-400"
          >
            <MuiLink
              color="inherit"
              href="/"
              className="flex items-center hover:text-blue-600"
              underline="hover"
            >
              <Home className="mr-1" fontSize="small" />
              Home
            </MuiLink>
            <MuiLink
              color="inherit"
              href="/project-tracker"
              className="flex items-center hover:text-blue-600"
              underline="hover"
            >
              <Assignment className="mr-1" fontSize="small" />
              Project Tracker
            </MuiLink>
            <Typography color="text.primary">
              {currentProject.name}
            </Typography>
          </Breadcrumbs>
        </div>

        {/* Project Header */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <Typography variant="h4" className="font-bold text-gray-900 dark:text-white mb-2">
                  {currentProject.name}
                </Typography>
                <Typography variant="body1" className="text-gray-600 dark:text-gray-400 mb-4">
                  {currentProject.description}
                </Typography>
                
                <div className="flex items-center gap-3 flex-wrap">
                  <Chip
                    label={currentProject.status?.replace('_', ' ')}
                    className={`${statusColor} font-medium`}
                    size="small"
                  />
                  <Chip
                    label={currentProject.priority}
                    className={`${priorityColor} font-medium`}
                    size="small"
                  />
                  <Chip
                    label={`Health: ${health}`}
                    className={`${healthColor} font-medium`}
                    size="small"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <EnhancedButton
                  variant="outlined"
                  startIcon={<Edit />}
                  onClick={() => {
                    // Handle edit project
                    console.log('Edit project:', currentProject.id);
                  }}
                >
                  Edit
                </EnhancedButton>
                
                <IconButton onClick={handleMenuOpen}>
                  <MoreVert />
                </IconButton>
              </div>
            </div>

            {/* Project Stats */}
            <Grid container spacing={3}>
              <Grid item xs={12} md={3}>
                <div className="text-center">
                  <Typography variant="h6" className="text-gray-900 dark:text-white">
                    {currentProject.progress_percentage || 0}%
                  </Typography>
                  <Typography variant="caption" className="text-gray-500">
                    Progress
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={currentProject.progress_percentage || 0}
                    className="mt-2 h-2 rounded-full"
                    sx={{
                      backgroundColor: 'rgba(0,0,0,0.1)',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: currentProject.progress_percentage >= 75 
                          ? '#10B981' 
                          : currentProject.progress_percentage >= 50 
                          ? '#3B82F6' 
                          : '#F59E0B',
                      },
                    }}
                  />
                </div>
              </Grid>

              <Grid item xs={12} md={3}>
                <div className="text-center">
                  <Typography variant="h6" className="text-gray-900 dark:text-white">
                    {milestones?.filter(m => m.status === 'completed').length || 0} / {milestones?.length || 0}
                  </Typography>
                  <Typography variant="caption" className="text-gray-500">
                    Milestones
                  </Typography>
                </div>
              </Grid>

              <Grid item xs={12} md={3}>
                <div className="text-center">
                  <Typography variant="h6" className="text-gray-900 dark:text-white">
                    {risks?.filter(r => r.status === 'open').length || 0}
                  </Typography>
                  <Typography variant="caption" className="text-gray-500">
                    Open Risks
                  </Typography>
                </div>
              </Grid>

              <Grid item xs={12} md={3}>
                <div className="text-center">
                  <div className="flex justify-center mb-2">
                    <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 32, height: 32 } }}>
                      {team?.map((member, index) => (
                        <Avatar key={index} alt={member.name}>
                          {member.name?.charAt(0)}
                        </Avatar>
                      )) || []}
                    </AvatarGroup>
                  </div>
                  <Typography variant="caption" className="text-gray-500">
                    Team Members
                  </Typography>
                </div>
              </Grid>
            </Grid>

            {/* Timeline Info */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                <div>
                  <span className="font-medium">Started:</span> {' '}
                  {currentProject.start_date ? 
                    new Date(currentProject.start_date).toLocaleDateString() : 
                    'Not set'
                  }
                </div>
                <div>
                  <span className="font-medium">Due:</span> {' '}
                  {currentProject.end_date ? 
                    formatDistanceToNow(new Date(currentProject.end_date), { addSuffix: true }) : 
                    'Not set'
                  }
                </div>
                <div>
                  <span className="font-medium">Owner:</span> {' '}
                  {currentProject.owner || 'Unassigned'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Project Details Tabs */}
        <Card>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              aria-label="project detail tabs"
              className="px-6"
            >
              {tabs.map((tab, index) => (
                <Tab
                  key={index}
                  label={
                    <div className="flex items-center space-x-2">
                      {tab.icon}
                      <span>{tab.label}</span>
                    </div>
                  }
                  id={`project-detail-tab-${index}`}
                  aria-controls={`project-detail-tabpanel-${index}`}
                  className="normal-case"
                />
              ))}
            </Tabs>
          </Box>

          {/* Tab Panels */}
          {tabs.map((tab, index) => (
            <TabPanel key={index} value={currentTab} index={index}>
              <div className="px-6 pb-6">
                {tab.component}
              </div>
            </TabPanel>
          ))}
        </Card>

        {/* Menu */}
        <Menu
          anchorEl={menuAnchor}
          open={Boolean(menuAnchor)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => { handleMenuClose(); /* Export */ }}>
            Export Project
          </MenuItem>
          <MenuItem onClick={() => { handleMenuClose(); /* Archive */ }}>
            Archive Project
          </MenuItem>
          <Divider />
          <MenuItem 
            onClick={() => { handleMenuClose(); /* Delete */ }}
            className="text-red-600"
          >
            Delete Project
          </MenuItem>
        </Menu>
      </div>
    </div>
  );
}
// Next, React, Tailwind
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Material UI
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Breadcrumbs,
  Link as MuiLink
} from '@mui/material';
import { Home, Assignment, BarChart } from '@mui/icons-material';

// Components
import Layout from '../../layouts/module/project-tracker';
import { EmptyState } from '../../components/ui/empty-state';

// Others
import { setBreadCrumbsList } from '../../utils/store/simiReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  const dispatch = useDispatch();

  useEffect(() => {
    // Set breadcrumbs
    dispatch(setBreadCrumbsList([
      { label: 'Home', linkTo: '/', icon: <Home fontSize="small" /> },
      { label: 'Project Tracker', linkTo: '/project-tracker', icon: <Assignment fontSize="small" /> },
      { label: 'Analytics', linkTo: '/project-tracker/analytics', icon: <BarChart fontSize="small" /> }
    ]));
  }, [dispatch]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-6">
        {/* Breadcrumbs */}
        <div className="mb-6">
          <Breadcrumbs 
            aria-label="breadcrumb"
            className="text-gray-600 dark:text-gray-400"
          >
            <MuiLink
              color="inherit"
              href="/"
              className="flex items-center hover:text-blue-600"
              underline="hover"
            >
              <Home className="mr-1" fontSize="small" />
              Home
            </MuiLink>
            <MuiLink
              color="inherit"
              href="/project-tracker"
              className="flex items-center hover:text-blue-600"
              underline="hover"
            >
              <Assignment className="mr-1" fontSize="small" />
              Project Tracker
            </MuiLink>
            <Typography color="text.primary" className="flex items-center">
              <BarChart className="mr-1" fontSize="small" />
              Analytics
            </Typography>
          </Breadcrumbs>
        </div>

        {/* Page Header */}
        <div className="mb-8">
          <Typography variant="h4" className="font-bold text-gray-900 dark:text-white mb-2">
            Project Analytics
          </Typography>
          <Typography variant="body1" className="text-gray-600 dark:text-gray-400">
            Advanced analytics and insights for project performance
          </Typography>
        </div>

        {/* Content */}
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent className="p-8">
                <EmptyState
                  title="Analytics Coming Soon"
                  description="Advanced project analytics and reporting features are currently under development. This page will include detailed charts, performance metrics, and insights to help you track project success."
                  icon={<BarChart className="w-16 h-16 text-gray-400" />}
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </div>
    </div>
  );
}
